<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\EmailTemplate;

class EmailVerificationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $verificationToken;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $verificationToken)
    {
        $this->verificationToken = $verificationToken;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        // Use frontend URL from JOBON_WEBAPP_URL environment variable
        $webAppUrl = rtrim(env('JOBON_WEBAPP_URL', config('app.url')), '/');
        $verificationUrl = $webAppUrl . '/user/verify-email?token=' . $this->verificationToken . '&email=' . urlencode($notifiable->email);

        // Try to get email template from database
        $template = EmailTemplate::where('slug', 'email-verification')->first();

        if ($template) {
            $locale = request()->hasHeader('Accept-Language') ?
                      request()->header('Accept-Language') :
                      app()->getLocale();

            $data = [
                '{{user_name}}' => $notifiable->name,
                '{{verification_url}}' => $verificationUrl,
                '{{company_name}}' => config('app.name'),
                '{{app_url}}' => config('app.url'),
            ];

            $subject = is_array($template->title) ?
                       ($template->title[$locale] ?? $template->title['en'] ?? 'Verify Your Email Address') :
                       $template->title;

            $content = is_array($template->content) ?
                       ($template->content[$locale] ?? $template->content['en'] ?? '') :
                       $template->content;

            $buttonText = is_array($template->button_text) ?
                          ($template->button_text[$locale] ?? $template->button_text['en'] ?? 'Verify Email') :
                          $template->button_text;

            // Replace placeholders
            foreach ($data as $placeholder => $value) {
                $content = str_replace($placeholder, $value, $content);
                $subject = str_replace($placeholder, $value, $subject);
                $buttonText = str_replace($placeholder, $value, $buttonText);
            }

            $mailMessage = (new MailMessage)
                ->subject($subject)
                ->line($content)
                ->action($buttonText, $verificationUrl)
                ->line('If you did not create an account, no further action is required.')
                ->line('This verification link will expire in 24 hours.');
        } else {
            // Fallback if no template found
            $mailMessage = (new MailMessage)
                ->subject('Verify Your Email Address')
                ->greeting('Hello ' . $notifiable->name . '!')
                ->line('Please click the button below to verify your email address.')
                ->action('Verify Email Address', $verificationUrl)
                ->line('If you did not create an account, no further action is required.')
                ->line('This verification link will expire in 24 hours.');
        }

        // Try to add email tracking if available
        try {
            if (class_exists('Modules\EmailTracking\Services\TrackableEmail')) {
                // Use reflection to add tracking dynamically
                $trackableEmailTrait = new \ReflectionClass('Modules\EmailTracking\Services\TrackableEmail');
                if ($trackableEmailTrait->hasMethod('makeTrackable')) {
                    // Create a temporary class that uses the trait
                    $trackableClass = new class {
                        use \Modules\EmailTracking\Services\TrackableEmail;

                        public function addTracking($mailMessage, $notifiable) {
                            return $this->makeTrackable($mailMessage, $notifiable);
                        }
                    };

                    return $trackableClass->addTracking($mailMessage, $notifiable);
                }
            }
        } catch (\Exception $e) {
            // Log the error but continue without tracking
            \Log::warning('Email tracking failed for verification email', [
                'error' => $e->getMessage(),
                'user_id' => $notifiable->id ?? null
            ]);
        }

        // Return the mail message without tracking if tracking fails
        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'email_verification',
            'user_id' => $notifiable->id,
            'email' => $notifiable->email,
            'verification_token' => $this->verificationToken,
        ];
    }
}