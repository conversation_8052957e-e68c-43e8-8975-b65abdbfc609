<?php

namespace App\Services;

use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use App\Services\BusinessDiscoveryService;
use App\Mail\JobNotificationApprovalMail;
use App\Mail\JobNotificationMail;
use App\Enums\JobNotificationStatusEnum;
use App\Enums\JobNotificationRecipientStatusEnum;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class JobNotificationService
{
    protected $businessDiscoveryService;

    public function __construct(BusinessDiscoveryService $businessDiscoveryService)
    {
        $this->businessDiscoveryService = $businessDiscoveryService;
    }

    /**
     * Process business discovery and setup notification campaign
     *
     * @param JobNotificationCampaign $campaign
     * @param string $zipCode
     * @param string|null $categoryId
     * @param float|null $radius
     * @return bool Returns true if businesses were found and campaign was set up
     */
    public function processBusinessDiscovery(
        JobNotificationCampaign $campaign,
        string $zipCode,
        ?string $categoryId = null,
        ?float $radius = null
    ): bool {
        $radius = $radius ?? (float) config('job_notification.default_radius');

        // Find businesses within radius
        $businesses = $this->businessDiscoveryService->findBusinesses(
            $zipCode,
            $categoryId,
            $radius
        );

        // Update campaign with business count
        $campaign->business_count = $businesses->count();
        $campaign->save();

        Log::info('Found businesses for job notification', [
            'job_id' => $campaign->job_id,
            'campaign_id' => $campaign->id,
            'business_count' => $campaign->business_count,
        ]);

        if ($campaign->business_count > 0) {
            // Create recipient records for each business
            $this->createRecipients($campaign, $businesses);

            // Send notifications directly to businesses (no admin approval needed)
            $this->sendNotificationsToBusinesses($campaign);

            Log::info('Job notification processed successfully and sent to businesses', [
                'job_id' => $campaign->job_id,
                'campaign_id' => $campaign->id,
                'business_count' => $campaign->business_count,
            ]);

            return true;
        }

        return false;
    }

    /**
     * Create recipient records for each business
     *
     * @param JobNotificationCampaign $campaign
     * @param \Illuminate\Support\Collection $businesses
     */
    public function createRecipients(JobNotificationCampaign $campaign, $businesses): void
    {
        foreach ($businesses as $business) {
            JobNotificationRecipient::create([
                'job_notification_campaign_id' => $campaign->id,
                'business_id' => $business->id,
                'business_name' => $business->name,
                'business_email' => $business->email,
                'business_phone' => $business->phone,
                'business_address' => $business->address,
                'distance' => $business->distance ?? null,
                'status' => JobNotificationRecipientStatusEnum::PENDING,
            ]);
        }

        Log::info('Created recipient records', [
            'campaign_id' => $campaign->id,
            'recipient_count' => $businesses->count(),
        ]);
    }

    /**
     * Send admin approval email
     *
     * @param JobNotificationCampaign $campaign
     */
    public function sendAdminApprovalEmail(JobNotificationCampaign $campaign): void
    {
        try {
            $adminEmail = config('job_notification.admin_email');

            // Generate admin token if not exists
            if (!$campaign->admin_token || $campaign->isTokenExpired()) {
                $campaign->generateAdminToken();
            }

            Mail::to($adminEmail)->send(new JobNotificationApprovalMail($campaign, $campaign->admin_token));

            Log::info('Admin approval email sent', [
                'campaign_id' => $campaign->id,
                'admin_email' => $adminEmail,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send admin approval email', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Send notifications directly to all businesses (no admin approval needed)
     *
     * @param JobNotificationCampaign $campaign
     */
    public function sendNotificationsToBusinesses(JobNotificationCampaign $campaign): void
    {
        // Update campaign status to approved and set approved_at timestamp
        $campaign->update([
            'status' => JobNotificationStatusEnum::APPROVED,
            'approved_at' => now(),
        ]);

        $recipients = $campaign->recipients()->where('status', JobNotificationRecipientStatusEnum::PENDING)->get();

        Log::info('Starting to send notifications to businesses', [
            'campaign_id' => $campaign->id,
            'total_recipients' => $recipients->count(),
        ]);

        $sentCount = 0;
        $failedCount = 0;

        foreach ($recipients as $recipient) {
            try {
                Mail::to($recipient->business_email)->send(new JobNotificationMail($campaign, $recipient));

                $recipient->update([
                    'status' => JobNotificationRecipientStatusEnum::SENT,
                    'sent_at' => now(),
                ]);

                $sentCount++;

                Log::info('Notification sent to business', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'business_email' => $recipient->business_email,
                    'business_name' => $recipient->business_name,
                ]);
            } catch (\Exception $e) {
                $recipient->update([
                    'status' => JobNotificationRecipientStatusEnum::FAILED,
                    'failed_at' => now(),
                    'failure_reason' => $e->getMessage(),
                ]);

                $failedCount++;

                Log::error('Failed to send notification to business', [
                    'campaign_id' => $campaign->id,
                    'recipient_id' => $recipient->id,
                    'business_email' => $recipient->business_email,
                    'business_name' => $recipient->business_name,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Update campaign status to sent and set counts
        $campaign->update([
            'status' => JobNotificationStatusEnum::SENT,
            'sent_at' => now(),
            'sent_count' => $sentCount,
            'failed_count' => $failedCount,
        ]);

        Log::info('Campaign notifications completed', [
            'campaign_id' => $campaign->id,
            'total_recipients' => $recipients->count(),
            'sent_count' => $sentCount,
            'failed_count' => $failedCount,
        ]);
    }

    /**
     * Check if a campaign can be retried
     *
     * @param JobNotificationCampaign $campaign
     * @return bool
     */
    public function canRetry(JobNotificationCampaign $campaign): bool
    {
        $maxRetries = config('job_notification.max_retry_attempts', 3);
        $retryableStatuses = [
            JobNotificationStatusEnum::PENDING_SCRAPING,
            JobNotificationStatusEnum::PENDING_RETRY,
        ];

        return in_array($campaign->status, $retryableStatuses) &&
               ($campaign->retry_count ?? 0) < $maxRetries;
    }

    /**
     * Update retry tracking for a campaign
     *
     * @param JobNotificationCampaign $campaign
     * @param int $retryAttempt
     * @param string|null $error
     */
    public function updateRetryTracking(JobNotificationCampaign $campaign, int $retryAttempt, ?string $error = null): void
    {
        $campaign->update([
            'retry_count' => $retryAttempt,
            'last_retry_at' => now(),
            'retry_error' => $error,
        ]);

        Log::info('Updated retry tracking for campaign', [
            'campaign_id' => $campaign->id,
            'retry_count' => $retryAttempt,
            'has_error' => !is_null($error),
        ]);
    }
}
