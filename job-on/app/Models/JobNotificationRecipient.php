<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\JobNotificationRecipientStatusEnum;

class JobNotificationRecipient extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'job_notification_campaign_id',
        'business_id',
        'business_name',
        'business_email',
        'business_phone',
        'business_address',
        'distance',
        'status',
        'sent_at',
        'failed_at',
        'failure_reason',
        'error',
        'open_count',
        'click_count',
        'last_opened_at',
        'last_clicked_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'distance' => 'float',
        'status' => JobNotificationRecipientStatusEnum::class,
        'sent_at' => 'datetime',
        'failed_at' => 'datetime',
        'open_count' => 'integer',
        'click_count' => 'integer',
        'last_opened_at' => 'datetime',
        'last_clicked_at' => 'datetime',
    ];

    /**
     * Get the campaign that owns the recipient.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(JobNotificationCampaign::class, 'job_notification_campaign_id');
    }

    /**
     * Get the business that owns the recipient.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Scope a query to only include pending recipients.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include sent recipients.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope a query to only include failed recipients.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Check if the notification is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the notification was sent successfully.
     */
    public function isSent(): bool
    {
        return $this->status === 'sent';
    }

    /**
     * Check if the notification failed to send.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the recipient has opened the email.
     */
    public function hasOpened(): bool
    {
        return $this->open_count > 0;
    }

    /**
     * Check if the recipient has clicked a link in the email.
     */
    public function hasClicked(): bool
    {
        return $this->click_count > 0;
    }
} 