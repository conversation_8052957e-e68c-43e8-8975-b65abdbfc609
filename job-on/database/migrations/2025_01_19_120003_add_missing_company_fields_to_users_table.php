<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing company fields only if they don't exist
            if (!Schema::hasColumn('users', 'company_name')) {
                $table->string('company_name')->nullable()->after('experience_duration');
            }
            if (!Schema::hasColumn('users', 'company_email')) {
                $table->string('company_email')->nullable()->after('company_name');
            }
            if (!Schema::hasColumn('users', 'company_phone')) {
                $table->bigInteger('company_phone')->nullable()->after('company_email');
            }
            if (!Schema::hasColumn('users', 'company_code')) {
                $table->bigInteger('company_code')->nullable()->after('company_phone');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'company_name',
                'company_email',
                'company_phone',
                'company_code'
            ]);
        });
    }
};
