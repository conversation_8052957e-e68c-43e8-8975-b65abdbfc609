<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Only add company fields that don't exist yet
            if (!Schema::hasColumn('users', 'company_name')) {
                $table->string('company_name')->nullable()->after('experience_duration');
            }
            if (!Schema::hasColumn('users', 'company_email')) {
                $table->string('company_email')->nullable()->after('company_name');
            }
            if (!Schema::hasColumn('users', 'company_phone')) {
                $table->bigInteger('company_phone')->nullable()->after('company_email');
            }
            if (!Schema::hasColumn('users', 'company_code')) {
                $table->bigInteger('company_code')->nullable()->after('company_phone');
            }

            // Skip service and location fields as they already exist from previous migration
            // Skip professional fields as they already exist from previous migration
            // Skip business fields as they already exist from previous migration
            // Skip document fields as they already exist from previous migration
            // Skip additional business info fields as they already exist from previous migration
            // Skip preferences as they already exist from previous migration
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Only drop company fields that were added by this migration
            $table->dropColumn([
                'company_name',
                'company_email',
                'company_phone',
                'company_code'
            ]);
        });
    }
};