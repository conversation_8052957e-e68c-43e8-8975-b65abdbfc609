<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_notification_recipients', function (Blueprint $table) {
            $table->text('business_address')->nullable()->after('business_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_notification_recipients', function (Blueprint $table) {
            $table->dropColumn('business_address');
        });
    }
};
