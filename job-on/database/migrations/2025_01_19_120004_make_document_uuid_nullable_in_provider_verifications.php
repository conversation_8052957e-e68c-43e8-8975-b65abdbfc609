<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only run if provider_verifications table exists
        if (Schema::hasTable('provider_verifications')) {
            Schema::table('provider_verifications', function (Blueprint $table) {
                // Drop the foreign key constraint first
                $table->dropForeign(['document_uuid']);

                // Make document_uuid nullable
                $table->string('document_uuid')->nullable()->change();

                // Re-add the foreign key constraint
                $table->foreign('document_uuid')->references('uuid')->on('assets')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only run if provider_verifications table exists
        if (Schema::hasTable('provider_verifications')) {
            Schema::table('provider_verifications', function (Blueprint $table) {
                // Drop the foreign key constraint
                $table->dropForeign(['document_uuid']);

                // Make document_uuid not nullable
                $table->string('document_uuid')->nullable(false)->change();

                // Re-add the foreign key constraint
                $table->foreign('document_uuid')->references('uuid')->on('assets')->onDelete('cascade');
            });
        }
    }
};
