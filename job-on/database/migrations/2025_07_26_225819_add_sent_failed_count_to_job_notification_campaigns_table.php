<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_notification_campaigns', function (Blueprint $table) {
            $table->integer('sent_count')->default(0)->after('business_count');
            $table->integer('failed_count')->default(0)->after('sent_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_notification_campaigns', function (Blueprint $table) {
            $table->dropColumn(['sent_count', 'failed_count']);
        });
    }
};
