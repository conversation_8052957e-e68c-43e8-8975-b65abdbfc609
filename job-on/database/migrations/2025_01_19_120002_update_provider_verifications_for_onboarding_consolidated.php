<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only run if provider_verifications table exists
        if (Schema::hasTable('provider_verifications')) {
            Schema::table('provider_verifications', function (Blueprint $table) {
                // Drop the existing foreign key constraint first
                $table->dropForeign(['document_uuid']);

                // Make document_uuid nullable and add new business fields
                $table->string('document_uuid')->nullable()->change();

                // Only add columns if they don't exist
                if (!Schema::hasColumn('provider_verifications', 'business_license_number')) {
                    $table->string('business_license_number')->nullable()->after('document_type');
                }
                if (!Schema::hasColumn('provider_verifications', 'tax_id')) {
                    $table->string('tax_id')->nullable()->after('business_license_number');
                }
                if (!Schema::hasColumn('provider_verifications', 'business_registration_state')) {
                    $table->string('business_registration_state', 2)->nullable()->after('tax_id');
                }
                if (!Schema::hasColumn('provider_verifications', 'submitted_at')) {
                    $table->timestamp('submitted_at')->nullable()->after('business_registration_state');
                }
                if (!Schema::hasColumn('provider_verifications', 'documents')) {
                    $table->json('documents')->nullable()->after('submitted_at');
                }

                // Re-add the foreign key constraint with SET NULL on delete
                $table->foreign('document_uuid')->references('uuid')->on('assets')->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Only run if provider_verifications table exists
        if (Schema::hasTable('provider_verifications')) {
            Schema::table('provider_verifications', function (Blueprint $table) {
                // Drop the foreign key constraint
                $table->dropForeign(['document_uuid']);

                // Remove the new columns
                $table->dropColumn([
                    'business_license_number',
                    'tax_id',
                    'business_registration_state',
                    'submitted_at',
                    'documents'
                ]);

                // Make document_uuid not nullable and re-add foreign key
                $table->string('document_uuid')->nullable(false)->change();
                $table->foreign('document_uuid')->references('uuid')->on('assets')->onDelete('cascade');
            });
        }
    }
};
